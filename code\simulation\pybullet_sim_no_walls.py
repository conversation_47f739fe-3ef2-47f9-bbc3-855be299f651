import pybullet as p
import pybullet_data
from box import Box
from pallet import Pallet
import random
from dataclasses import dataclass

@dataclass
class BulletSimNoWalls:

    pallet: Pallet
    mass : int = 5

    def __init__(self, pallet: Pallet):
        self.pallet = pallet

        # Start PyBullet-session  
        physicsClient = p.connect(p.DIRECT) # p.DIRECT or p.GUI

        # Speed up somulation
        p.setRealTimeSimulation(0)
        p.setTimeStep(1./120.)
        
        # Initialise PyBullet
        p.setAdditionalSearchPath(pybullet_data.getDataPath())
        p.setGravity(0, 0, -5)
        

        # Initialise Vieuw
        cameraDistance = 2
        cameraYaw = 50  
        cameraPitch = -35  
        cameraTargetPosition = [self.pallet.width, self.pallet.depth, 1] 
        p.resetDebugVisualizerCamera(cameraDistance, cameraYaw, cameraPitch, cameraTargetPosition)

        # Baseplane
        planeId = p.loadURDF("plane.urdf")

        # Initialise the pallet
        palletHalfExtents = [dim/2 for dim in pallet.dimensions]
        startPos_pallet = [palletHalfExtents[0], palletHalfExtents[1], -palletHalfExtents[2]]
        startOrientation_pallet = p.getQuaternionFromEuler([0, 0, 0])
        collisionShapeId_pallet = p.createCollisionShape(p.GEOM_BOX, halfExtents = palletHalfExtents)
        visualShapeId_pallet = p.createVisualShape(p.GEOM_BOX, halfExtents=palletHalfExtents, rgbaColor=[0.647, 0.165, 0.165, 1])  # Pallet has brown colour
        palletId = p.createMultiBody(baseMass=0, baseCollisionShapeIndex=collisionShapeId_pallet, baseVisualShapeIndex=visualShapeId_pallet,
                                basePosition=startPos_pallet, baseOrientation=startOrientation_pallet)

    def simulate_box(self, box: Box, location):

        box.x, box.y, box.z = location[0], location[1], 1.5*self.pallet.max_height # Drop from high

        # Initialise box
        startPosbox = [box.x+box.width/2, box.y+box.depth/2, box.z]
        startOrientationbox = p.getQuaternionFromEuler([box.roll, box.pitch, box.yaw])
        boxHalfExtents = [box.width/2, box.depth/2, box.height/2] 

        # Create box
        collisionShapeId_box = p.createCollisionShape(p.GEOM_BOX, halfExtents=boxHalfExtents)
        r = random.uniform(0, 1) # Randomising the colour of the box 
        g = random.uniform(0, 1)  
        b = random.uniform(0, 1)  
        visualShapeId_box = p.createVisualShape(p.GEOM_BOX, halfExtents=boxHalfExtents, rgbaColor=[r, g, b, 1]) 
        boxId = p.createMultiBody(baseMass=(self.mass), baseCollisionShapeIndex=collisionShapeId_box, baseVisualShapeIndex=visualShapeId_box, 
                                basePosition=startPosbox, baseOrientation=startOrientationbox)

        # Start simulation
        for i in range(250):
            p.stepSimulation()

            # Check if the box is stabilized (using velocity threshold as an indicator)
            lin_vel, ang_vel = p.getBaseVelocity(boxId)
            if all(abs(v) < 0.01 for v in lin_vel) and all(abs(v) < 0.01 for v in ang_vel):
                break  # Exit loop early if the box has settled

    def place_box(self, box: Box):

        # Initialise box
        startPosbox = [box.x+box.width/2, box.y+box.depth/2, box.z+box.height/2]
        startOrientationbox = p.getQuaternionFromEuler([box.roll, box.pitch, box.yaw])
        boxHalfExtents = [box.width/2, box.depth/2, box.height/2] 

        # Create box
        collisionShapeId_box = p.createCollisionShape(p.GEOM_BOX, halfExtents=boxHalfExtents)
        r = random.uniform(0, 1) # Randomising the colour of the box 
        g = random.uniform(0, 1)  
        b = random.uniform(0, 1)  
        visualShapeId_box = p.createVisualShape(p.GEOM_BOX, halfExtents=boxHalfExtents, rgbaColor=[r, g, b, 1]) 
        boxId = p.createMultiBody(baseMass=(0), baseCollisionShapeIndex=collisionShapeId_box, baseVisualShapeIndex=visualShapeId_box, 
                                basePosition=startPosbox, baseOrientation=startOrientationbox)

        # Start simulation
        for i in range(250):
            p.stepSimulation()

            # Check if the box is stabilized (using velocity threshold as an indicator)
            lin_vel, ang_vel = p.getBaseVelocity(boxId)
            if all(abs(v) < 0.01 for v in lin_vel) and all(abs(v) < 0.01 for v in ang_vel):
                break  # Exit loop early if the box has settled
