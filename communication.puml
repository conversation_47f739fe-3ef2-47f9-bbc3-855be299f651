@startuml Bleckmann

hide empty members
' skinparam nodesep 8
' skinparam ranksep 5

<style>
.Physical{
  BackgroundColor #bbbbbb
}
</style>

hide <<Physical>> circle
hide <<Physical>> stereotype

class Main
class Simulation{
  +add_box(box)
  +get_next_best_step()
}
class Robot{
  +pick_place(pick, place)
}
class robot <<Physical>>
class Conveyor{
  +add_box_at_pose(box, pose)
  +get_box_pose(box)
  +eject_box()
}
class conveyor <<Physical>>
class Controllino
class controllino <<Physical>>
class sensors <<Physical>>
class ConveyorLock <<asyncio.Lock>>{
  +acquire()
  +release()
}
class "Barcode scanner" as BarcodeScanner
class "barcode scanner" as barcodeScanner <<Physical>>
class "Box scanner" as BoxScanner

Main -u(0- Simulation: > Add box
Main -u(0- Simulation: < Get next best step
' A step can be: Pick & Place, Eject

Main -l(0- Robot: > Pick & Place
Robot --- ConveyorLock
ConveyorLock - Conveyor

Main -(0-- Conveyor: < Get box pose
Main -(0-- Conveyor: > Add box at pose
Main -(0-- Conveyor: > Eject box

Main -r0)- BarcodeScanner: < New barcode
Main -r0)- BoxScanner: < New box
BoxScanner -[hidden]- BarcodeScanner

Robot -l robot: OPCUA
Conveyor -- Controllino
Controllino - controllino: > TCP/IP
controllino - conveyor: > I/O
controllino - sensors: < I/O
conveyor -[hidden]- sensors

BarcodeScanner - barcodeScanner: < ?

@enduml
