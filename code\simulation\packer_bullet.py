import math
import os
import time
from pathlib import Path
from packer_help_functions import take_screenshot
from packer_score_functions import (
    calculate_score_gaps,
    calculate_score_height,
    calculate_score_skewed,
    calculate_score_volume,
)
from pallet import Pallet
from pybullet_sim import BulletSim
from box import Box
import random
import pybullet as p
import copy
from dataclasses import dataclass
from videomaker import create_video_from_images


@dataclass
class BulletPacker:
    pallet: Pallet

    # Amount of simulations that will be run for boxes inside the buffer or que (more simulations for directly out of que)
    amount_of_simulations_buffer: int = 80
    amount_of_simulations_direct: int = 115

    # Scoring factors for scoring a simulation of a placed box
    score_factor_direct: float = 1.07
    factor_score_gaps: float = 30
    factor_score_skewed: float = 4
    factor_score_height: float = 30
    factor_score_volume: float = 0

    distance_between: float = 0.015

    def calculate_score_and_final_location_for_box_drop_in_location(
        self, pallet_situation: Pallet, box: Box, droplocation, max_skewdness
    ):
        # If droplocation outside of pallet, return score of 0
        if (
            droplocation[0] + box.width > pallet_situation.width + 0.005
            or droplocation[1] + box.depth > pallet_situation.depth + 0.005
            or droplocation[0] + 0.005 < 0
            or droplocation[1] + 0.005 < 0
        ):
            return [0], (0, 0, 0), (0, 0, 0)

        self.bulletsim.simulate_box(box, droplocation)

        # Get the index of the last layed box
        i = p.getNumBodies() - 1

        # Get exact location after drop (rounding to snap to a grid a litle and the minus to work with center in bottem left corner)
        pos, orn = p.getBasePositionAndOrientation(i)
        pos = list(pos)
        pos[0] = pos[0] - box.width / 2
        pos[1] = pos[1] - box.depth / 2
        pos[2] = pos[2] - box.height / 2
        pos = tuple(pos)
        euler = p.getEulerFromQuaternion(orn)

        score_skewed = calculate_score_skewed(p, i) * self.factor_score_skewed

        score_height = (
            calculate_score_height(pallet_situation, p, i) * self.factor_score_height
        )  # The further away from the max height, the better

        score_gaps = (
            calculate_score_gaps(self.pallet, p, i, box, self.distance_between)
            * self.factor_score_gaps
        )

        if (
            abs(euler[0]) > math.radians(max_skewdness)
            or abs(euler[1]) > math.radians(max_skewdness)
            or abs(euler[2]) > math.radians(max_skewdness)
        ):
            scores = [0]
        elif score_height < 0:
            # if highest point of box higher then limit, return score of 0
            scores = [0]
        elif score_gaps == (-20):
            scores = [0]
        else:
            # calculate score based on how much boxes are next to eachoter and the walls
            score_volume = calculate_score_volume(box) * self.factor_score_volume

            scores = [score_height, score_gaps, score_volume, score_skewed]

        p.removeBody(i)

        return scores, pos, euler

    def calculate_best_drop_location(
        self, pallet: Pallet, box: Box, amount_of_simulations, max_skewdness
    ):
        best_score = -1
        best_location = None
        drop_locations = set()

        # add all corners as possible drop location
        drop_locations.add((0, 0))
        drop_locations.add((pallet.width - box.width, 0))
        drop_locations.add((0, pallet.depth - box.depth))
        drop_locations.add((pallet.width - box.width, pallet.depth - box.depth))

        # add the corners and points that make the boxes line up of the last 5 placed boxes
        last_five_boxes = pallet.packed_boxes[-5:]
        for packed_box in last_five_boxes:
            # For location of the corners on top of the box
            drop_locations.add((packed_box.x, packed_box.y))
            drop_locations.add((packed_box.x + packed_box.width - box.width, packed_box.y))
            drop_locations.add((packed_box.x, packed_box.y + packed_box.depth - box.depth))
            drop_locations.add(
                (
                    packed_box.x + packed_box.width - box.width,
                    packed_box.y + packed_box.depth - box.depth,
                )
            )

            # Below the box
            drop_locations.add((packed_box.x, packed_box.y - box.depth - self.distance_between))
            drop_locations.add(
                (
                    packed_box.x + packed_box.width - box.width,
                    packed_box.y - box.depth - self.distance_between,
                )
            )

            # To the right of the box
            drop_locations.add(
                (packed_box.x + packed_box.width + self.distance_between, packed_box.y)
            )
            drop_locations.add(
                (
                    packed_box.x + packed_box.width + self.distance_between,
                    packed_box.y + packed_box.depth - box.depth,
                )
            )

            # Above the box
            drop_locations.add(
                (packed_box.x, packed_box.y + packed_box.depth + self.distance_between)
            )
            drop_locations.add(
                (
                    packed_box.x + packed_box.width - box.width,
                    packed_box.y + packed_box.depth + self.distance_between,
                )
            )

            # To the left of the box
            drop_locations.add((packed_box.x - box.width - self.distance_between, packed_box.y))
            drop_locations.add(
                (
                    packed_box.x - box.width - self.distance_between,
                    packed_box.y + packed_box.depth - box.depth,
                )
            )

        if len(drop_locations) < amount_of_simulations:
            # add random location for the rest
            for i in range(amount_of_simulations - len(drop_locations)):
                drop_x = random.uniform(0, pallet.width - box.width)
                drop_y = random.uniform(0, pallet.depth - box.depth)
                drop_locations.add((drop_x, drop_y))

        # try dropping boxes at the drop locations and calculate the best location to drop
        for location in drop_locations:
            drop_location = [location[0], location[1]]
            scores, pos, euler = self.calculate_score_and_final_location_for_box_drop_in_location(
                pallet, box, drop_location, max_skewdness
            )
            final_location_orientation = pos, euler

            if sum(scores) > best_score:
                best_score = sum(scores)
                best_location = final_location_orientation
                best_drop_location = drop_location
                best_scores = copy.deepcopy(scores)

        return best_score, best_location, best_scores, best_drop_location

    def calculate_best_box(self, pallet, boxes, max_skewdness=90):
        # Always place the first box in the left corner and return a score of 100
        if len(self.pallet.packed_boxes) == 0:
            return 100, ((0, 0, 0), (0, 0, 0)), boxes[-1], (0, 0)

        best_score = -1
        best_location_orientation = None
        best_box = None
        amount_of_simulations = 0
        score_factor = 1

        for box in boxes:
            if (
                box == boxes[-1]
            ):  # Give higher score to direct placement of box (last box in queue) instead of using buffer
                amount_of_simulations = self.amount_of_simulations_direct
                score_factor = self.score_factor_direct
            else:
                amount_of_simulations = self.amount_of_simulations_buffer

            # get the score, location and orientation of the best drop location
            score, location_orientation, scores, drop_location = self.calculate_best_drop_location(
                pallet, box, amount_of_simulations, max_skewdness
            )

            new_score = score * score_factor

            if new_score > best_score:
                best_score = score
                best_location_orientation = location_orientation
                best_box = box
                best_drop_loc = drop_location

        # if all boxes can't be place return the box in the que
        if best_score == 0:
            best_box = boxes[-1]

        return (best_score, best_location_orientation, best_box, best_drop_loc)

    def pack_boxes(
        self,
        pallet: Pallet,
        buffer: list,
        queue: list,
        max_buffer: int,
        disable_prints=False,
        max_skewdness=90,
        create_video=False,
    ):
        max_volume = pallet.width * pallet.max_height * pallet.depth
        volume = 0
        counter = 0
        box_buffer = buffer
        box_queue = queue
        visible_box_queue = None
        self.bulletsim = BulletSim(self.pallet)

        if create_video:
            # Create images directory if it doesn't exist
            images_dir = Path("images")
            images_dir.mkdir(exist_ok=True)

            # Remove all files in the images directory
            for file_path in images_dir.iterdir():
                if file_path.is_file():
                    file_path.unlink()

        t0 = time.time()

        # keep goint while they're boxes left in the buffer or que
        while box_buffer or box_queue:
            # Boxes that are able to be placed
            visible_box_queue = copy.deepcopy(box_buffer)
            if box_queue:
                visible_box_queue.append(
                    box_queue[0]
                )  # Boxes that are free to place, last is the direct (non buffer) box

            counter += 1
            score, location_orientation, box, drop_location = self.calculate_best_box(
                pallet, visible_box_queue, max_skewdness
            )

            # if score is 0, remove box from que cause it could not be placed
            if score == 0:
                if box in box_queue:
                    if len(box_buffer) < max_buffer:
                        box_queue.remove(box)
                        box_buffer.append(box)
                        if not disable_prints:
                            print(f"COULDN'T PLACE BOX SO MOVED BOX TO BUFFER")
                            counter -= 1
                    else:
                        if not disable_prints:
                            print(
                                f"NOT ABLE TO PLACE BOX WITH DIM {box.dim} buffer = {len(box_buffer)}, queue = {len(box_queue)}, total = {len(box_buffer) + len(box_queue)}"
                            )
                        break
                else:
                    if not disable_prints:
                        print(
                            f"NOT ABLE TO PLACE BOX WITH DIM {box.dim} buffer = {len(box_buffer)}, queue = {len(box_queue)}, total = {len(box_buffer) + len(box_queue)}"
                        )
                    break
            else:
                if box in box_queue:
                    img_name = f"images/best_box_{counter}"
                    box_queue.remove(box)
                    if not disable_prints:
                        print(
                            f"DIRECT: buffer = {len(box_buffer)}, queue = {len(box_queue)}, total = {len(box_buffer) + len(box_queue)}"
                        )
                else:
                    img_name = f"images/best_box_{counter}"
                    box_buffer.remove(box)
                    if not disable_prints:
                        print(
                            f"BUFFER: buffer = {len(box_buffer)}, queue = {len(box_queue)}, total = {len(box_buffer) + len(box_queue)}"
                        )

                # give the box the right location and orientation to drop for real
                box.place(
                    location_orientation[0][0],
                    location_orientation[0][1],
                    location_orientation[0][2],
                )
                box.roll, box.pitch, box.yaw = (
                    location_orientation[1][0],
                    location_orientation[1][1],
                    location_orientation[1][2],
                )

                # place the box for real
                self.bulletsim.place_box(box)

                volume = volume + box.depth * box.height * box.width

                pallet.packed_boxes.append(box)
                if create_video:
                    take_screenshot(img_name, pallet)
                if not disable_prints:
                    print(
                        f"Box {counter} with score {score:.3f} dropped on location {[round(loc, 2) for loc in drop_location]}, ended on location {[round(loc, 2) for loc in location_orientation[0]]}-------------------------------------------------"
                    )
                    print("")

        t1 = time.time()
        total_time = t1 - t0
        volume_filled = round(100 * volume / max_volume, 2)

        print("--------------------")
        print(
            f"amount of boxes left: {len(buffer) + len(queue)}/{len(buffer) + len(queue) + len(pallet.packed_boxes)}"
        )
        print(f"volume used: {volume_filled}%")
        print(
            f"total time: {round(total_time, 2)}s with average of {round(total_time / len(pallet.packed_boxes), 2)}s per box"
        )

        p.disconnect()

        if create_video:
            create_video_from_images()

        return volume_filled, total_time
